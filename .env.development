ENVIRONMENT=development
SECRET_KEY=dev-secret-key-change-in-production
DEBUG=true
LOG_LEVEL=INFO

# External APIs (optional for development)
GITHUB_TOKEN=your_dev_github_token
OPENAI_API_KEY=********************************************************

# Development-specific settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
API_KEY= "API_KEY_DEV"
GITHUB_PRIVATE_KEY="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"