# Makefile
.PHONY: dev prod test dev-docker prod-docker

dev:
	ENVIRONMENT=development uvicorn platyfend_ai.api.main:app --reload --env-file .env.development

prod:
	ENVIRONMENT=production gunicorn platyfend_ai.api.main:app -c gunicorn.conf.py

test:
	ENVIRONMENT=testing pytest --env-file .env.testing

dev-docker:
	docker-compose -f docker-compose.yml up

prod-docker:
	docker-compose -f docker-compose.prod.yml up -d
