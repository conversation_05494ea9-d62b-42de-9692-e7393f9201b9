#!/usr/bin/env python3
import os
import sys
from pathlib import Path

import uvicorn

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Set development environment
os.environ["ENVIRONMENT"] = "development"

if __name__ == "__main__":
    uvicorn.run(
        "platyfend_ai.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["platyfend_ai"],
        env_file=".env.development",
    )
