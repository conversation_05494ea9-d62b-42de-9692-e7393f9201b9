{"cells": [{"cell_type": "code", "execution_count": 55, "id": "5536d48f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200\n", "diff --git a/test_pr/file1.py b/test_pr/file1.py\n", "new file mode 100644\n", "index 0000000..cb07d11\n", "--- /dev/null\n", "+++ b/test_pr/file1.py\n", "@@ -0,0 +1,4 @@\n", "+import pandas as pd\n", "+\n", "+\n", "+print(1)\n", "\n"]}], "source": ["import requests\n", "\n", "url = \"https://api.github.com/repos/MrLeritaite/NaturalLanguageProcessing/pulls/4.diff\"\n", "headers = {\n", "    \"Authorization\": \"Bearer ****************************************\",\n", "    \"Accept\": \"application/vnd.github.v3.diff\",\n", "}\n", "r = requests.get(url, headers=headers)\n", "print(r.status_code)\n", "print(r.text[:200])\n"]}, {"cell_type": "code", "execution_count": 46, "id": "c625f894", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["401\n", "{\"message\":\"A JSON web token could not be decoded\",\"documentation_url\":\"https://docs.github.com/rest\",\"status\":\"401\"}\n"]}], "source": ["url = \"https://api.github.com/app/installations/********/access_tokens\"\n", "r = requests.post(url)\n", "print(r.status_code)\n", "print(r.text)"]}, {"cell_type": "code", "execution_count": null, "id": "cac21697", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n"]}], "source": ["import jwt\n", "import time\n", "\n", "# Load your GitHub App’s private key\n", "private_key = \"\"\"-----BEGIN RSA PRIVATE KEY-----\n", "MIIEowIBAAKCAQEAzOzucfaVipQsp1CyNl4mvIseGBOHI9S8I698xib29jXIFCr7\n", "6O4tqHEnXB599/XnG3lX/ngPTa1dB07VBHNjrnyrihz5yBix1btRPvNviTX3gyR0\n", "EkZW/bGbx6diicAamVbcbsuDWb8nk5xPMKBSC5Enmm0rKO/zXPU7BIPa9hl+C3Hf\n", "5qX1c+xCposHHHbyRBqhGoTlMVzfmQRltKKNxzlOA/SKOn+Ged4COF41sp4cmFix\n", "WC0EMFk4bAOjsPgah1Vyt9qi7WufSB3ObNCH04D2CuQYB1dUB8mrSy2aJKK2/OsH\n", "wQu92fRMZvddkP4qC58z8Pg5bu3OyxwOu6X9iQIDAQABAoIBABAOdHRrfLYZ/BG0\n", "CfKml+RoBhie1xTgo0ksg9tGzEzoU9/4qjpLBzrOeuYF4ZrfIpeNf26J27EbZ1Ge\n", "RKSPofzW+d3dfVjHTo/PmIMVMeUPXHBjF9Eiv8gq9WjEXb0rVyoymxz3Rzht+Nzg\n", "/3dw6FzNp8u6yAnyGyBqepBVAIANxVeqvWZc45Qap1z90lV3Mcvlee1a9SadWJPA\n", "OFYCkWoj1HsC2+9MfmpmZwxJUOfsSXIwnQ8X8yMLLROp02wiirXNwz1HxMCvSiId\n", "w2VKaVeD5XgWGJ6/BcKjuTqy2t7nxzHAzO1fV2KtOuklChDXbnhO+L0AY5l7b/mJ\n", "kpFoFgECgYEA8a769r29NbdE++j2YghprcvGd4Ku8vjBwrLmqnQv/Xyqkl6rX9hW\n", "hf0ROPIynS4Ly02K9Qnvwxg2ywY2M8fgXVcJxQb8cMwShL+peWjApZMls2o5mYS/\n", "jY4cds8E1/0mCuItxU2hX+0sG8MRLzsSdSzp4q/Mwb1KaDoBaZum10ECgYEA2RCI\n", "Z/utYDeU6CKs243rGrzdRQVroeC9DcnPKEVWhkj/+GAr2qi7Yydb/oibgh8b8iWo\n", "1MxrTs3vq4YQFmy1fA/qtd1jGtNsmwlIPFXhyYcJmaxnQX8rt2+ugfyg7YoEEhRC\n", "L6W0aX16MGwQsifH8hqC0CJajLBXGfFLfyMnnEkCgYBhoCYeD2zjyuEhxOvpk65k\n", "luDXm8Fhwlw6cpu8lOxhv78DGxysva3Nt/kCAMuzNEttVyhfbSEdtKVFg3/XkNCv\n", "w53dqcHFKpn8Z5NkyIyxFWxwOeOEgNwDqTkUQPGa8dYxJUYJ4h7iqeJcYIKLipgd\n", "HwRnHk5eoEDzMyMo7sgJQQKBgQCd0IdcEn9bFQ4OOCe0+NmV9vpo1CgM6XiKLc+P\n", "V9owh6KS0lkQAN08mcEoreliEXwf8YFuNbFO+irmafIIW8EXEftzfiIvvseXcIg0\n", "Vbxy6Nrx/4aS+AKwu2IE38eg7FdJTbFRMwiYKcJMskTxQjr8qomxCUkMQKkTE3vw\n", "U37ZAQKBgDqpHS3xj0Klqotmq87EiGyjmrwlt+tM/DKYcPRGgR34EVn9l9JuNU95\n", "obQebgBd8TbsFBEDlIXTBJOZo38hup2n+a7X1/K51fi/DE0j/AkBnl60WhLmVVTL\n", "6IHFl1kuHJk+q/1h3Sb3XKBbmlyiS/mJ6F986j77kkrSDCZXYVad\n", "-----END RSA PRIVATE KEY-----\"\"\"\n", "\n", "app_id = 1605045  # from GitHub App settings\n", "\n", "now = int(time.time())\n", "payload = {\n", "    # issued at time\n", "    \"iat\": now,\n", "    # JWT expiration time (max 10 minutes)\n", "    \"exp\": now + 600,\n", "    # GitHub App's identifier\n", "    \"iss\": app_id\n", "}\n", "\n", "jwt_token = jwt.encode(payload, private_key, algorithm=\"RS256\")\n", "print(jwt_token)\n"]}, {"cell_type": "code", "execution_count": 22, "id": "20ed1911", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["401\n", "{'message': 'A JSON web token could not be decoded', 'documentation_url': 'https://docs.github.com/rest', 'status': '401'}\n"]}], "source": ["import requests\n", "\n", "installation_id = ********\n", "url = f\"https://api.github.com/app/installations/{installation_id}/access_tokens\"\n", "\n", "headers = {\n", "    \"Authorization\": f\"Bearer {jwt_token}\",\n", "    \"Accept\": \"application/vnd.github+json\"\n", "}\n", "\n", "r = requests.post(url, headers=headers)\n", "print(r.status_code)\n", "print(r.json())\n"]}, {"cell_type": "code", "execution_count": 31, "id": "ecd469c1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Status: 401\n", "{'message': 'A JSON web token could not be decoded', 'documentation_url': 'https://docs.github.com/rest', 'status': '401'}\n"]}], "source": ["import jwt\n", "import time\n", "import requests\n", "\n", "APP_ID = 1605045      # <-- GitHub App ID (integer from settings, not installation ID)\n", "INSTALLATION_ID = ********\n", "PRIVATE_KEY_PATH = \"key.pem\"\n", "\n", "# Load private key\n", "with open(PRIVATE_KEY_PATH, \"r\") as f:\n", "    private_key = f.read()\n", "\n", "# Create JWT\n", "now = int(time.time())\n", "payload = {\n", "    \"iat\": now - 60,\n", "    \"exp\": now + 600,  # 10 minutes max\n", "    \"iss\": APP_ID,\n", "}\n", "\n", "jwt_token = jwt.encode(payload, private_key, algorithm=\"RS256\")\n", "if isinstance(jwt_token, bytes):\n", "    jwt_token = jwt_token.decode(\"utf-8\")\n", "\n", "# Exchange JWT for installation token\n", "url = f\"https://api.github.com/app/installations/{INSTALLATION_ID}/access_tokens\"\n", "headers = {\n", "    \"Authorization\": f\"Bearer {jwt_token}\",\n", "    \"Accept\": \"application/vnd.github+json\"\n", "}\n", "r = requests.post(url, headers=headers)\n", "\n", "print(\"Status:\", r.status_code)\n", "print(r.json())\n"]}, {"cell_type": "code", "execution_count": 30, "id": "8584facd", "metadata": {}, "outputs": [{"data": {"text/plain": ["'**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["jwt_token"]}, {"cell_type": "code", "execution_count": 32, "id": "d3de73ca", "metadata": {}, "outputs": [], "source": ["from cryptography.hazmat.primitives import serialization\n", "from cryptography.hazmat.primitives.serialization import load_pem_private_key\n", "\n", "# Load the PKCS#1 private key\n", "with open(\"key.pem\", \"rb\") as f:\n", "    private_key_data = f.read()\n", "\n", "# Parse the private key\n", "private_key = load_pem_private_key(private_key_data, password=None)\n", "\n", "# Convert to PKCS#8 format\n", "pkcs8_private_key = private_key.private_bytes(\n", "    encoding=serialization.Encoding.PEM,\n", "    format=serialization.PrivateFormat.PKCS8,\n", "    encryption_algorithm=serialization.NoEncryption()\n", ")\n", "\n", "# Use this for JWT encoding\n", "jwt_token = jwt.encode(payload, pkcs8_private_key, algorithm=\"RS256\")"]}, {"cell_type": "code", "execution_count": 33, "id": "115d797c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["jwt_token"]}, {"cell_type": "code", "execution_count": 40, "id": "9d55b3d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Status: 401\n", "Response: {'message': 'A JSON web token could not be decoded', 'documentation_url': 'https://docs.github.com/rest', 'status': '401'}\n"]}], "source": ["import jwt\n", "import time\n", "import requests\n", "from cryptography.hazmat.primitives import serialization\n", "from cryptography.hazmat.primitives.serialization import load_pem_private_key\n", "\n", "APP_ID = 1605045\n", "INSTALLATION_ID = ********\n", "\n", "# Load and convert private key\n", "with open(\"key.pem\", \"rb\") as f:\n", "    private_key_data = f.read()\n", "\n", "# Parse the private key (handles both PKCS#1 and PKCS#8)\n", "private_key_obj = load_pem_private_key(private_key_data, password=None)\n", "\n", "# Convert to PKCS#8 PEM format (what PyJWT expects)\n", "private_key_pem = private_key_obj.private_bytes(\n", "    encoding=serialization.Encoding.PEM,\n", "    format=serialization.PrivateFormat.PKCS8,\n", "    encryption_algorithm=serialization.NoEncryption()\n", ")\n", "\n", "# Create JWT\n", "now = int(time.time())\n", "payload = {\n", "    \"iat\": now - 60,    # Issued 60 seconds ago to account for clock skew\n", "    \"exp\": now + 600,   # Expires in 10 minutes (GitHub's max)\n", "    \"iss\": APP_ID,      # GitHub App ID\n", "}\n", "\n", "# Generate JWT token\n", "jwt_token = jwt.encode(payload, private_key_pem, algorithm=\"RS256\")\n", "\n", "# Exchange JWT for installation access token\n", "url = f\"https://api.github.com/app/installations/{INSTALLATION_ID}/access_tokens\"\n", "headers = {\n", "    \"Authorization\": f\"Bearer ****************************************\",\n", "    \"Accept\": \"application/vnd.github.v3.diff\",\n", "}\n", "\n", "response = requests.post(url, headers=headers)\n", "print(\"Status:\", response.status_code)\n", "print(\"Response:\", response.json())"]}, {"cell_type": "code", "execution_count": 54, "id": "0d858da6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Status: 200\n", "Response: {'success': True, 'installation_id': '********', 'token': '****************************************', 'expires_in_hours': 1, 'organization': {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'type': 'personal', 'provider': 'github'}, 'generated_at': '2025-08-21T17:32:05.493Z'}\n"]}], "source": ["url = \"http://localhost:3001/api/github/token/********\"\n", "response = requests.get(url)\n", "print(\"Status:\", response.status_code)\n", "print(\"Response:\", response.json())"]}, {"cell_type": "code", "execution_count": null, "id": "bd233a77", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "pentest-copilot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}