import asyncio
import logging
import tempfile
import shutil
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Type
from uuid import uuid4

from platyfend_ai.models.analysis import (
    AnalysisResult,
    AnalyzerType,
    SecurityFinding,
    PRAnalysisReport,
)
from platyfend_ai.config.settings import settings

logger = logging.getLogger(__name__)


class AnalyzerError(Exception):
    """Exception raised when an analyzer fails"""
    pass


class BaseAnalyzer(ABC):
    """Abstract base class for all code analyzers"""
    
    def __init__(self, name: str, analyzer_type: AnalyzerType, version: Optional[str] = None):
        """
        Initialize the analyzer.
        
        Args:
            name: Human-readable name of the analyzer
            analyzer_type: Type of analyzer
            version: Version of the analyzer tool
        """
        self.name = name
        self.analyzer_type = analyzer_type
        self.version = version
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @abstractmethod
    async def analyze(self, diff_content: str, files_info: Dict[str, Any], temp_dir: Path) -> AnalysisResult:
        """
        Analyze the provided code diff.
        
        Args:
            diff_content: The diff content to analyze
            files_info: Information about changed files
            temp_dir: Temporary directory for analysis
            
        Returns:
            Analysis result containing findings
            
        Raises:
            AnalyzerError: If analysis fails
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """
        Check if the analyzer tool is available and properly configured.
        
        Returns:
            True if analyzer is available, False otherwise
        """
        pass
    
    def get_supported_file_extensions(self) -> List[str]:
        """
        Get list of file extensions this analyzer supports.
        
        Returns:
            List of file extensions (e.g., ['.py', '.js'])
        """
        return []
    
    def should_analyze_file(self, file_path: str) -> bool:
        """
        Determine if this analyzer should analyze the given file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file should be analyzed, False otherwise
        """
        supported_extensions = self.get_supported_file_extensions()
        if not supported_extensions:
            return True  # Analyze all files if no specific extensions
        
        file_ext = Path(file_path).suffix.lower()
        return file_ext in supported_extensions


class AnalysisEngine:
    """Core analysis engine that orchestrates multiple analyzers"""
    
    def __init__(self):
        """Initialize the analysis engine"""
        self.analyzers: Dict[str, BaseAnalyzer] = {}
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("Analysis engine initialized")
    
    def register_analyzer(self, analyzer: BaseAnalyzer) -> None:
        """
        Register an analyzer with the engine.
        
        Args:
            analyzer: Analyzer instance to register
        """
        if not isinstance(analyzer, BaseAnalyzer):
            raise ValueError("Analyzer must inherit from BaseAnalyzer")
        
        if not analyzer.is_available():
            self.logger.warning(f"Analyzer {analyzer.name} is not available, skipping registration")
            return
        
        self.analyzers[analyzer.name] = analyzer
        self.logger.info(f"Registered analyzer: {analyzer.name} ({analyzer.analyzer_type})")
    
    def unregister_analyzer(self, analyzer_name: str) -> None:
        """
        Unregister an analyzer from the engine.
        
        Args:
            analyzer_name: Name of the analyzer to unregister
        """
        if analyzer_name in self.analyzers:
            del self.analyzers[analyzer_name]
            self.logger.info(f"Unregistered analyzer: {analyzer_name}")
        else:
            self.logger.warning(f"Analyzer {analyzer_name} not found for unregistration")
    
    def get_registered_analyzers(self) -> List[str]:
        """
        Get list of registered analyzer names.
        
        Returns:
            List of analyzer names
        """
        return list(self.analyzers.keys())
    
    def get_analyzer(self, analyzer_name: str) -> Optional[BaseAnalyzer]:
        """
        Get a specific analyzer by name.
        
        Args:
            analyzer_name: Name of the analyzer
            
        Returns:
            Analyzer instance or None if not found
        """
        return self.analyzers.get(analyzer_name)
    
    async def analyze_pr(
        self,
        diff_content: str,
        files_info: Dict[str, Any],
        pr_id: int,
        pr_number: int,
        repository: str,
        analyzer_names: Optional[List[str]] = None
    ) -> PRAnalysisReport:
        """
        Analyze a pull request using registered analyzers.
        
        Args:
            diff_content: The diff content to analyze
            files_info: Information about changed files
            pr_id: Pull request ID
            pr_number: Pull request number
            repository: Repository name
            analyzer_names: Optional list of specific analyzers to run
            
        Returns:
            Complete analysis report
        """
        started_at = datetime.utcnow()
        
        self.logger.info(f"Starting analysis for PR #{pr_number} in {repository}")
        
        # Determine which analyzers to run
        analyzers_to_run = self._get_analyzers_to_run(analyzer_names)
        
        if not analyzers_to_run:
            self.logger.warning("No analyzers available for analysis")
            return PRAnalysisReport(
                pr_id=pr_id,
                pr_number=pr_number,
                repository=repository,
                started_at=started_at,
                completed_at=datetime.utcnow(),
                success=False,
                errors=["No analyzers available"]
            )
        
        # Create temporary directory for analysis
        with tempfile.TemporaryDirectory(prefix="platyfend_analysis_") as temp_dir:
            temp_path = Path(temp_dir)
            self.logger.debug(f"Created temporary directory: {temp_path}")
            
            # Run analyzers concurrently
            analysis_tasks = []
            for analyzer_name, analyzer in analyzers_to_run.items():
                task = self._run_analyzer_safely(analyzer, diff_content, files_info, temp_path)
                analysis_tasks.append(task)
            
            # Wait for all analyzers to complete
            analysis_results = await asyncio.gather(*analysis_tasks, return_exceptions=True)
        
        # Process results
        successful_results = []
        errors = []
        
        for i, result in enumerate(analysis_results):
            analyzer_name = list(analyzers_to_run.keys())[i]
            
            if isinstance(result, Exception):
                error_msg = f"Analyzer {analyzer_name} failed: {str(result)}"
                self.logger.error(error_msg)
                errors.append(error_msg)
            elif isinstance(result, AnalysisResult):
                successful_results.append(result)
                self.logger.info(f"Analyzer {analyzer_name} completed with {result.total_findings} findings")
            else:
                error_msg = f"Analyzer {analyzer_name} returned unexpected result type"
                self.logger.error(error_msg)
                errors.append(error_msg)
        
        # Create analysis report
        completed_at = datetime.utcnow()
        report = PRAnalysisReport(
            pr_id=pr_id,
            pr_number=pr_number,
            repository=repository,
            started_at=started_at,
            completed_at=completed_at,
            analysis_results=successful_results,
            success=len(errors) == 0,
            errors=errors
        )
        
        # Calculate summary statistics
        report.calculate_summary()
        
        self.logger.info(
            f"Analysis completed for PR #{pr_number}: "
            f"{report.total_findings} total findings, "
            f"{report.critical_findings} critical, "
            f"{report.high_findings} high severity"
        )
        
        return report
    
    def _get_analyzers_to_run(self, analyzer_names: Optional[List[str]]) -> Dict[str, BaseAnalyzer]:
        """
        Get the analyzers that should be run.
        
        Args:
            analyzer_names: Optional list of specific analyzer names
            
        Returns:
            Dictionary of analyzer name to analyzer instance
        """
        if analyzer_names is None:
            # Run all registered analyzers
            return self.analyzers.copy()
        
        # Run only specified analyzers
        analyzers_to_run = {}
        for name in analyzer_names:
            if name in self.analyzers:
                analyzers_to_run[name] = self.analyzers[name]
            else:
                self.logger.warning(f"Requested analyzer {name} is not registered")
        
        return analyzers_to_run
    
    async def _run_analyzer_safely(
        self,
        analyzer: BaseAnalyzer,
        diff_content: str,
        files_info: Dict[str, Any],
        temp_dir: Path
    ) -> AnalysisResult:
        """
        Run an analyzer with error handling.
        
        Args:
            analyzer: Analyzer to run
            diff_content: Diff content to analyze
            files_info: File information
            temp_dir: Temporary directory
            
        Returns:
            Analysis result
            
        Raises:
            AnalyzerError: If analyzer fails
        """
        try:
            self.logger.debug(f"Running analyzer: {analyzer.name}")
            result = await analyzer.analyze(diff_content, files_info, temp_dir)
            
            if not isinstance(result, AnalysisResult):
                raise AnalyzerError(f"Analyzer {analyzer.name} returned invalid result type")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Analyzer {analyzer.name} failed: {e}", exc_info=True)
            raise AnalyzerError(f"Analyzer {analyzer.name} failed: {e}")


# Global analysis engine instance
analysis_engine = AnalysisEngine()
