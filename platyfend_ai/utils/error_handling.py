import logging
import traceback
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import uuid4

from pydantic import BaseModel


class ErrorSeverity(str, Enum):
    """Error severity levels"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """Error categories for better organization"""

    NETWORK = "network"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    VALIDATION = "validation"
    ANALYSIS = "analysis"
    GITHUB_API = "github_api"
    CONFIGURATION = "configuration"
    INTERNAL = "internal"
    RATE_LIMIT = "rate_limit"
    TIMEOUT = "timeout"


class ErrorContext(BaseModel):
    """Context information for errors"""

    error_id: str
    timestamp: datetime
    severity: ErrorSeverity
    category: ErrorCategory
    component: str
    operation: str
    message: str
    details: Optional[Dict[str, Any]] = None
    stack_trace: Optional[str] = None
    user_message: Optional[str] = None
    recovery_suggestions: List[str] = []


class PlatyfendError(Exception):
    """Base exception class for Platyfend AI errors"""

    def __init__(
        self,
        message: str,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        category: ErrorCategory = ErrorCategory.INTERNAL,
        component: str = "unknown",
        operation: str = "unknown",
        details: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None,
        recovery_suggestions: Optional[List[str]] = None,
    ):
        super().__init__(message)

        self.context = ErrorContext(
            error_id=str(uuid4()),
            timestamp=datetime.now(timezone.utc),
            severity=severity,
            category=category,
            component=component,
            operation=operation,
            message=message,
            details=details or {},
            stack_trace=traceback.format_exc(),
            user_message=user_message,
            recovery_suggestions=recovery_suggestions or [],
        )


class AnalysisError(PlatyfendError):
    """Errors related to code analysis"""

    def __init__(self, message: str, analyzer: str = "unknown", **kwargs):
        kwargs.setdefault("category", ErrorCategory.ANALYSIS)
        kwargs.setdefault("component", f"analyzer.{analyzer}")
        super().__init__(message, **kwargs)


class GitHubAPIError(PlatyfendError):
    """Errors related to GitHub API operations"""

    def __init__(self, message: str, status_code: Optional[int] = None, **kwargs):
        kwargs.setdefault("category", ErrorCategory.GITHUB_API)
        kwargs.setdefault("component", "github_api")
        if status_code:
            kwargs.setdefault("details", {}).update({"status_code": status_code})
        super().__init__(message, **kwargs)


class AuthenticationError(PlatyfendError):
    """Authentication-related errors"""

    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("category", ErrorCategory.AUTHENTICATION)
        kwargs.setdefault("severity", ErrorSeverity.HIGH)
        super().__init__(message, **kwargs)


class RateLimitError(PlatyfendError):
    """Rate limiting errors"""

    def __init__(self, message: str, reset_time: Optional[str] = None, **kwargs):
        kwargs.setdefault("category", ErrorCategory.RATE_LIMIT)
        kwargs.setdefault("severity", ErrorSeverity.MEDIUM)
        if reset_time:
            kwargs.setdefault("details", {}).update({"reset_time": reset_time})
        super().__init__(message, **kwargs)


class ConfigurationError(PlatyfendError):
    """Configuration-related errors"""

    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("category", ErrorCategory.CONFIGURATION)
        kwargs.setdefault("severity", ErrorSeverity.HIGH)
        super().__init__(message, **kwargs)


class ValidationError(PlatyfendError):
    """Data validation errors"""

    def __init__(self, message: str, **kwargs):
        kwargs.setdefault("category", ErrorCategory.VALIDATION)
        kwargs.setdefault("severity", ErrorSeverity.MEDIUM)
        super().__init__(message, **kwargs)


class TimeoutError(PlatyfendError):
    """Timeout-related errors"""

    def __init__(
        self, message: str, timeout_duration: Optional[float] = None, **kwargs
    ):
        kwargs.setdefault("category", ErrorCategory.TIMEOUT)
        kwargs.setdefault("severity", ErrorSeverity.MEDIUM)
        if timeout_duration:
            kwargs.setdefault("details", {}).update(
                {"timeout_duration": timeout_duration}
            )
        super().__init__(message, **kwargs)


class ErrorHandler:
    """Centralized error handling and logging"""

    def __init__(self, logger_name: str = __name__):
        self.logger = logging.getLogger(logger_name)
        self.error_history: List[ErrorContext] = []

    def handle_error(
        self,
        error: Exception,
        component: str = "unknown",
        operation: str = "unknown",
        additional_context: Optional[Dict[str, Any]] = None,
    ) -> ErrorContext:
        """
        Handle and log an error with proper context.

        Args:
            error: The exception that occurred
            component: Component where error occurred
            operation: Operation being performed
            additional_context: Additional context information

        Returns:
            Error context object
        """
        if isinstance(error, PlatyfendError):
            context = error.context
            # Update context with additional information
            if additional_context:
                if context.details is None:
                    context.details = {}
                context.details.update(additional_context)
        else:
            # Convert regular exception to PlatyfendError
            context = ErrorContext(
                error_id=str(uuid4()),
                timestamp=datetime.now(timezone.utc),
                severity=ErrorSeverity.MEDIUM,
                category=ErrorCategory.INTERNAL,
                component=component,
                operation=operation,
                message=str(error),
                details=additional_context or {},
                stack_trace=traceback.format_exc(),
            )

        # Log the error
        self._log_error(context)

        # Store in history (keep last 100 errors)
        self.error_history.append(context)
        if len(self.error_history) > 100:
            self.error_history.pop(0)

        return context

    def _log_error(self, context: ErrorContext):
        """Log error with appropriate level based on severity"""
        log_message = f"[{context.error_id}] {context.component}.{context.operation}: {context.message}"

        if context.details:
            log_message += f" | Details: {context.details}"

        if context.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, exc_info=True)
        elif context.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, exc_info=True)
        elif context.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)

    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of recent errors"""
        if not self.error_history:
            return {"total_errors": 0, "by_category": {}, "by_severity": {}}

        by_category = {}
        by_severity = {}

        for error in self.error_history:
            # Count by category
            category = error.category.value
            by_category[category] = by_category.get(category, 0) + 1

            # Count by severity
            severity = error.severity.value
            by_severity[severity] = by_severity.get(severity, 0) + 1

        return {
            "total_errors": len(self.error_history),
            "by_category": by_category,
            "by_severity": by_severity,
            "recent_errors": [
                {
                    "error_id": error.error_id,
                    "timestamp": error.timestamp.isoformat(),
                    "severity": error.severity.value,
                    "category": error.category.value,
                    "component": error.component,
                    "message": error.message,
                }
                for error in self.error_history[-10:]  # Last 10 errors
            ],
        }

    def clear_error_history(self):
        """Clear error history"""
        self.error_history.clear()
        self.logger.info("Error history cleared")


# Global error handler instance
error_handler = ErrorHandler("platyfend_ai.errors")


def handle_exceptions(component: str, operation: str = "unknown"):
    """
    Decorator for automatic exception handling.

    Args:
        component: Component name
        operation: Operation name
    """

    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                context = error_handler.handle_error(e, component, operation)
                # Re-raise as PlatyfendError if it wasn't already
                if not isinstance(e, PlatyfendError):
                    raise PlatyfendError(
                        message=str(e),
                        severity=context.severity,
                        category=context.category,
                        component=component,
                        operation=operation,
                    ) from e
                raise

        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = error_handler.handle_error(e, component, operation)
                # Re-raise as PlatyfendError if it wasn't already
                if not isinstance(e, PlatyfendError):
                    raise PlatyfendError(
                        message=str(e),
                        severity=context.severity,
                        category=context.category,
                        component=component,
                        operation=operation,
                    ) from e
                raise

        # Return appropriate wrapper based on function type
        import asyncio

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


def get_user_friendly_message(error: Exception) -> str:
    """
    Get a user-friendly error message.

    Args:
        error: The exception

    Returns:
        User-friendly error message
    """
    if isinstance(error, PlatyfendError) and error.context.user_message:
        return error.context.user_message

    # Default user-friendly messages based on error type
    if isinstance(error, AuthenticationError):
        return "Authentication failed. Please check your credentials and try again."
    elif isinstance(error, RateLimitError):
        return "Rate limit exceeded. Please wait a moment and try again."
    elif isinstance(error, TimeoutError):
        return "The operation timed out. Please try again later."
    elif isinstance(error, GitHubAPIError):
        return "GitHub API error occurred. Please check your permissions and try again."
    elif isinstance(error, AnalysisError):
        return "Analysis failed. Please check your code and try again."
    elif isinstance(error, ConfigurationError):
        return "Configuration error. Please check your settings."
    else:
        return "An unexpected error occurred. Please try again later."
