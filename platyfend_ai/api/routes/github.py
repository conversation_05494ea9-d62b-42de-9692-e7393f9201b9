import logging
import time
from typing import Any, Dict

import httpx
from fastapi import APIRouter, Body, Depends, HTTPException, status

from platyfend_ai.analyzers import AstGrepAnalyzer, LinterAnalyzer, SemgrepAnalyzer
from platyfend_ai.config.settings import settings
from platyfend_ai.core.analysis_engine import analysis_engine
from platyfend_ai.models.github import GitHubPRWebhookData
from platyfend_ai.services import (
    CommentGenerationError,
    Diff<PERSON><PERSON>cher,
    DiffFetchError,
    SecurityCommentGenerator,
)
from platyfend_ai.utils.error_handling import (
    AnalysisError,
    PlatyfendError,
    get_user_friendly_message,
    handle_exceptions,
)
from platyfend_ai.utils.security_service import SecurityService

logger = logging.getLogger(__name__)

# Simple in-memory cache to prevent duplicate analysis
# Format: {f"{repository}:{pr_number}:{action}": timestamp}
_analysis_cache = {}
CACHE_DURATION = 300  # 5 minutes

github_routes = APIRouter(
    tags=["GitHub"], dependencies=[Depends(SecurityService.get_api_key)]
)


async def send_comments_to_frontend(pr_data, comments, analysis_report):
    """
    Send security review comments to Next.js frontend instead of GitHub.

    Args:
        pr_data: GitHub PR webhook data
        comments: List of review comments
        analysis_report: Analysis results

    Returns:
        Dict with results of sending comments
    """
    frontend_url = getattr(settings, "frontend_url", "http://localhost:3001")
    endpoint = f"{frontend_url}/api/security-review"

    # Prepare payload for frontend
    payload = {
        "pr_info": {
            "id": pr_data.id,
            "number": pr_data.number,
            "title": pr_data.title,
            "repository": pr_data.repository,
            "author": pr_data.author,
            "url": pr_data.url,
            "base_branch": pr_data.base_branch,
            "head_branch": pr_data.head_branch,
            "action": pr_data.action,
        },
        "analysis_summary": {
            "total_findings": analysis_report.total_findings,
            "critical_findings": analysis_report.critical_findings,
            "high_findings": analysis_report.high_findings,
            "success": analysis_report.success,
            "analyzers_run": len(analysis_report.analysis_results),
        },
        "comments": [
            {
                "body": comment.body,
                "file_path": comment.file_path or "",
                "line": comment.line or 0,
                "severity": comment.severity.value if comment.severity else "",
                "comment_type": comment.comment_type,
                "finding_ids": comment.finding_ids,
            }
            for comment in comments
        ],
        "timestamp": time.time(),
    }

    try:
        # Get API key for frontend authentication from settings
        api_key = getattr(settings, "api_key", None)

        headers = {"Content-Type": "application/json"}
        if api_key:
            headers["x-api-key"] = api_key

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(endpoint, json=payload, headers=headers)

            if response.status_code == 200:
                logger.info(f"Successfully sent {len(comments)} comments to frontend")
                return {
                    "sent_comments": len(comments),
                    "errors": [],
                    "frontend_response": response.json() if response.content else None,
                }
            else:
                error_msg = (
                    f"Frontend returned status {response.status_code}: {response.text}"
                )
                logger.error(error_msg)
                return {"sent_comments": 0, "errors": [error_msg]}

    except httpx.RequestError as e:
        error_msg = f"Failed to connect to frontend: {e}"
        logger.error(error_msg)
        return {"sent_comments": 0, "errors": [error_msg]}
    except Exception as e:
        error_msg = f"Unexpected error sending to frontend: {e}"
        logger.error(error_msg)
        return {"sent_comments": 0, "errors": [error_msg]}


@github_routes.post("/pr_open", response_model=Dict[str, Any])
async def github_pr_open(
    pr_data: GitHubPRWebhookData = Body(..., description="GitHub PR webhook data")
) -> Dict[str, Any]:
    """
    Handle GitHub Pull Request webhook events.

    This endpoint receives GitHub PR webhook data when a PR is opened, updated, or closed.
    It processes the PR data and triggers the appropriate analysis workflows.

    Args:
        pr_data: GitHub PR webhook data containing all relevant PR information

    Returns:
        Dict containing the processing status and any relevant information

    Raises:
        HTTPException: If there's an error processing the PR data
    """
    try:
        logger.info(
            f"Received PR webhook for {pr_data.repository} - PR #{pr_data.number}: {pr_data.title}"
        )
        logger.debug(
            f"PR action: {pr_data.action}, state: {pr_data.state}, author: {pr_data.author}"
        )

        # Log key PR metrics
        logger.info(
            f"PR stats - Commits: {pr_data.commits}, "
            f"Files changed: {pr_data.changed_files}, "
            f"Additions: {pr_data.additions}, "
            f"Deletions: {pr_data.deletions}"
        )

        # Initialize the PR processing pipeline
        response_data = await process_pr_security_analysis(pr_data)

        return response_data

    except Exception as e:
        logger.error(f"Error processing PR webhook: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process PR webhook: {str(e)}",
        )


@handle_exceptions("github_routes", "process_pr_security_analysis")
async def process_pr_security_analysis(
    pr_data: GitHubPRWebhookData,
) -> Dict[str, Any]:
    """
    Process PR security analysis pipeline with strict sequential execution.

    Args:
        pr_data: GitHub PR webhook data

    Returns:
        Processing results
    """
    # Step tracking to ensure sequential execution
    current_step = 0
    total_steps = 5

    def log_step_start(step_num: int, description: str):
        nonlocal current_step
        if step_num != current_step + 1:
            logger.error(
                f"Step execution out of order! Expected step {current_step + 1}, got step {step_num}"
            )
            raise RuntimeError(
                f"Steps must execute sequentially. Expected {current_step + 1}, got {step_num}"
            )
        current_step = step_num
        logger.info(f"Step {step_num}/{total_steps}: {description}")

    def log_step_complete(step_num: int, description: str):
        logger.info(f"Step {step_num}/{total_steps} completed: {description}")

    try:
        logger.info(
            f"Starting security analysis pipeline for PR #{pr_data.number} in {pr_data.repository}"
        )

        # Skip analysis if the webhook was triggered by the bot itself to prevent loops
        bot_usernames = ["platyfend-bot", "platyfend[bot]", "platyfend-test[bot]"]
        if pr_data.sender in bot_usernames:
            logger.info(
                f"Skipping analysis - webhook triggered by bot user: {pr_data.sender}"
            )
            return {
                "status": "skipped",
                "reason": f"Bot-triggered event from {pr_data.sender}",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Only process specific PR actions that should trigger analysis
        allowed_actions = getattr(
            settings, "allowed_pr_actions", ["opened", "synchronize", "reopened"]
        )
        if pr_data.action not in allowed_actions:
            logger.info(
                f"Skipping analysis - action '{pr_data.action}' not in allowed actions: {allowed_actions}"
            )
            return {
                "status": "skipped",
                "reason": f"Action '{pr_data.action}' not configured for analysis",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Check if we've recently analyzed this PR to prevent duplicate processing
        cache_key = f"{pr_data.repository}:{pr_data.number}:{pr_data.action}"
        current_time = time.time()

        # Clean old cache entries (only remove expired ones)
        expired_keys = [
            key
            for key, timestamp in _analysis_cache.items()
            if current_time - timestamp > CACHE_DURATION
        ]
        for key in expired_keys:
            del _analysis_cache[key]

        if cache_key in _analysis_cache:
            time_diff = current_time - _analysis_cache[cache_key]
            if time_diff < CACHE_DURATION:
                logger.info(
                    f"Skipping analysis - recently processed {cache_key} ({time_diff:.1f}s ago)"
                )
                return {
                    "status": "skipped",
                    "reason": f"Recently analyzed ({time_diff:.1f}s ago)",
                    "pr_number": pr_data.number,
                    "repository": pr_data.repository,
                }

        # Mark this PR as being analyzed
        _analysis_cache[cache_key] = current_time

        # Skip analysis for draft PRs or closed PRs unless configured otherwise
        if pr_data.draft and not getattr(settings, "analyze_draft_prs", False):
            logger.info(f"Skipping analysis for draft PR #{pr_data.number}")
            return {
                "status": "skipped",
                "reason": "Draft PR - analysis disabled",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        if pr_data.action == "closed":
            logger.info(f"Skipping analysis for closed PR #{pr_data.number}")
            return {
                "status": "skipped",
                "reason": "PR closed",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Step 1: Fetch the diff/patch and file information
        log_step_start(1, "Fetching PR diff and file information")
        diff_fetcher = DiffFetcher(
            github_token=settings.github_token, use_dynamic_tokens=True
        )

        try:
            pr_data_complete = await diff_fetcher.fetch_pr_diff_and_files(
                pr_data.diff_url, pr_data.repository, pr_data.number
            )
            diff_content = pr_data_complete["diff_content"]
            files_info = pr_data_complete["files_info"]

            log_step_complete(
                1,
                f"Fetched diff ({len(diff_content)} chars) and {files_info['total_files']} files",
            )

        except DiffFetchError as e:
            logger.error(f"Failed to fetch PR data: {e}")
            return {
                "status": "error",
                "error": "Failed to fetch PR diff",
                "details": str(e),
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Step 2: Initialize and register analyzers
        log_step_start(2, "Initializing security analyzers")

        # Register analyzers based on configuration
        if getattr(settings, "enable_semgrep", True):
            semgrep_analyzer = SemgrepAnalyzer()
            analysis_engine.register_analyzer(semgrep_analyzer)

        if getattr(settings, "enable_ast_grep", True):
            ast_grep_analyzer = AstGrepAnalyzer()
            analysis_engine.register_analyzer(ast_grep_analyzer)

        if getattr(settings, "enable_linters", True):
            linter_analyzer = LinterAnalyzer()
            analysis_engine.register_analyzer(linter_analyzer)

        registered_analyzers = analysis_engine.get_registered_analyzers()
        log_step_complete(
            2,
            f"Registered {len(registered_analyzers)} analyzers: {registered_analyzers}",
        )

        if not registered_analyzers:
            logger.warning("No analyzers available for analysis")
            return {
                "status": "error",
                "error": "No analyzers available",
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Step 3: Run security analysis
        log_step_start(3, "Running security analysis")

        try:
            analysis_report = await analysis_engine.analyze_pr(
                diff_content=diff_content,
                files_info=files_info,
                pr_id=pr_data.id,
                pr_number=pr_data.number,
                repository=pr_data.repository,
            )

            log_step_complete(
                3,
                f"Analysis completed: {analysis_report.total_findings} findings, "
                f"{analysis_report.critical_findings} critical, "
                f"{analysis_report.high_findings} high severity",
            )

        except AnalysisError as e:
            logger.error(f"Analysis failed: {e}")
            return {
                "status": "error",
                "error": "Security analysis failed",
                "details": str(e),
                "pr_number": pr_data.number,
                "repository": pr_data.repository,
            }

        # Step 4: Generate review comments
        log_step_start(4, "Generating security review comments")

        if analysis_report.total_findings > 0:
            try:
                comment_generator = SecurityCommentGenerator(
                    settings.openai_api_key,
                    getattr(settings, "openai_model", "gpt-4o-mini"),
                )

                # Collect all findings from analysis results
                all_findings = []
                for result in analysis_report.analysis_results:
                    all_findings.extend(result.findings)

                # Generate comments
                review_comments = await comment_generator.generate_review_comments(
                    findings=all_findings,
                    pr_context={
                        "title": pr_data.title,
                        "repository": pr_data.repository,
                        "author": pr_data.author,
                        "number": pr_data.number,
                    },
                )

                log_step_complete(
                    4,
                    f"Generated {len(review_comments)} review comments for {len(all_findings)} findings",
                )

            except CommentGenerationError as e:
                logger.error(f"Comment generation failed: {e}")
                # Continue with empty comments - analysis results are still valuable
                review_comments = []
                log_step_complete(
                    4, "Comment generation failed, continuing with empty comments"
                )
        else:
            try:
                comment_generator = SecurityCommentGenerator(
                    settings.openai_api_key,
                    getattr(settings, "openai_model", "gpt-4o-mini"),
                )

                # Generate a positive security message when no issues are found
                review_comments = await comment_generator.generate_no_findings_message(
                    pr_context={
                        "title": pr_data.title,
                        "repository": pr_data.repository,
                        "author": pr_data.author,
                        "number": pr_data.number,
                    },
                )

                log_step_complete(4, "Generated positive security message for clean PR")

            except CommentGenerationError as e:
                logger.error(f"Failed to generate no-findings message: {e}")
                review_comments = []
                log_step_complete(
                    4,
                    "Failed to generate no-findings message, continuing with empty comments",
                )

        # Step 5: Send comments to Next.js frontend
        log_step_start(5, "Sending comments to Next.js frontend")

        comment_results = {"sent_comments": 0, "errors": []}

        if review_comments:
            try:
                # Send comments to Next.js frontend
                comment_results = await send_comments_to_frontend(
                    pr_data=pr_data,
                    comments=review_comments,
                    analysis_report=analysis_report,
                )

                log_step_complete(
                    5, f"Sent {comment_results['sent_comments']} comments to frontend"
                )

            except Exception as e:
                logger.error(f"Failed to send comments to frontend: {e}")
                comment_results["errors"].append(str(e))
                log_step_complete(5, f"Failed to send comments to frontend: {e}")
        else:
            log_step_complete(
                5, "No review comments generated - skipped comment sending"
            )

        # Prepare response
        response_data = {
            "status": "success",
            "message": f"Security analysis completed for PR #{pr_data.number}",
            "pr_info": {
                "id": pr_data.id,
                "number": pr_data.number,
                "title": pr_data.title,
                "repository": pr_data.repository,
                "action": pr_data.action,
                "author": pr_data.author,
                "base_branch": pr_data.base_branch,
                "head_branch": pr_data.head_branch,
                "draft": pr_data.draft,
                "mergeable": pr_data.mergeable,
                "stats": {
                    "commits": pr_data.commits,
                    "changed_files": pr_data.changed_files,
                    "additions": pr_data.additions,
                    "deletions": pr_data.deletions,
                },
            },
            "analysis_results": {
                "total_findings": analysis_report.total_findings,
                "critical_findings": analysis_report.critical_findings,
                "high_findings": analysis_report.high_findings,
                "analyzers_run": len(analysis_report.analysis_results),
                "success": analysis_report.success,
                "errors": analysis_report.errors,
            },
            "comment_results": comment_results,
            "processing_time": (
                (
                    analysis_report.completed_at - analysis_report.started_at
                ).total_seconds()
                if analysis_report.completed_at
                else None
            ),
        }

        logger.info(
            f"Successfully completed all {total_steps} steps of security analysis pipeline for PR #{pr_data.number}"
        )
        return response_data

    except PlatyfendError as e:
        logger.error(f"Platyfend error in PR processing: {e}")
        return {
            "status": "error",
            "error": get_user_friendly_message(e),
            "error_id": e.context.error_id if hasattr(e, "context") else None,
            "pr_number": pr_data.number,
            "repository": pr_data.repository,
        }
    except Exception as e:
        logger.error(f"Unexpected error in PR processing: {e}", exc_info=True)
        return {
            "status": "error",
            "error": "An unexpected error occurred during processing",
            "details": str(e),
            "pr_number": pr_data.number,
            "repository": pr_data.repository,
        }
