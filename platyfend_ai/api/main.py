import logging
import time
from contextlib import asynccontextmanager
from typing import Union

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from platyfend_ai.api.routes.endpoints import api_router
from platyfend_ai.config.settings import DevelopmentConfig, ProductionConfig, settings

# Configure logging based on environment
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    logger.info(f"Starting up in {settings.environment} mode...")
    yield
    logger.info("Shutting down...")


# Create FastAPI app with environment-specific settings
app = FastAPI(
    title=settings.app_name,
    description=settings.description,
    version=settings.version,
    docs_url=settings.docs_url,
    redoc_url=settings.redoc_url,
    lifespan=lifespan,
)

# Environment-specific CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)


# Add request timing middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Include API router
app.include_router(api_router, prefix="/api/v1")


# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    return {
        "message": "Welcome to PLATYFEND-AI API",
        "docs": "/docs",
        "version": settings.version,
    }


# Health check endpoints
@app.get("/health", tags=["Health"])
async def health_check():
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.version,
        "environment": settings.environment,
    }


@app.get("/ready", tags=["Health"])
async def readiness_check():
    # Add checks for dependencies (database, external APIs, etc.)
    return {"status": "ready"}


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(_request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected error occurred. Please try again later."},
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "platyfend_ai.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["platyfend_ai"],
    )
