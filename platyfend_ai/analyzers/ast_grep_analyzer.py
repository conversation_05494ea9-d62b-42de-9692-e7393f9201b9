import asyncio
import json
import logging
import shutil
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

from platyfend_ai.config.settings import settings
from platyfend_ai.core.analysis_engine import Ana<PERSON><PERSON>Error, BaseAnalyzer
from platyfend_ai.models.analysis import (
    AnalysisResult,
    AnalyzerType,
    CodeLocation,
    SecurityFinding,
    SeverityLevel,
)

logger = logging.getLogger(__name__)


class AstGrepAnalyzer(BaseAnalyzer):
    """AST-Grep pattern matching analyzer implementation"""

    def __init__(self, rules_config: Optional[Dict[str, Any]] = None):
        """
        Initialize the AST-Grep analyzer.

        Args:
            rules_config: Custom rules configuration for AST-Grep
        """
        super().__init__(
            "AST-Grep", AnalyzerType.AST_GREP, self._get_ast_grep_version()
        )

        self.rules_config = rules_config or self._get_default_rules()
        self.timeout = getattr(settings, "ast_grep_timeout", 60)

        self.logger.info(
            f"AST-Grep analyzer initialized with {len(self.rules_config)} rules"
        )

    def _get_ast_grep_version(self) -> Optional[str]:
        """Get AST-Grep version if available"""
        try:
            import subprocess

            result = subprocess.run(
                ["ast-grep", "--version"], capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                return result.stdout.strip()
        except Exception:
            pass
        return None

    def is_available(self) -> bool:
        """Check if AST-Grep is available"""
        return shutil.which("ast-grep") is not None

    def get_supported_file_extensions(self) -> List[str]:
        """Get file extensions supported by AST-Grep"""
        return [
            ".py",
            ".js",
            ".ts",
            ".jsx",
            ".tsx",
            ".java",
            ".go",
            ".rs",
            ".rb",
            ".php",
            ".c",
            ".cpp",
            ".cc",
            ".cxx",
            ".h",
            ".hpp",
            ".cs",
            ".kt",
            ".swift",
            ".scala",
            ".html",
            ".css",
            ".scss",
            ".vue",
        ]

    def _get_default_rules(self) -> Dict[str, Any]:
        """Get default security-focused AST-Grep rules"""
        return {
            "rules": [
                {
                    "id": "hardcoded-password",
                    "message": "Potential hardcoded password detected",
                    "severity": "HIGH",
                    "languages": ["python", "javascript", "typescript"],
                    "pattern": 'password = "$VALUE"',
                    "constraints": {"VALUE": {"regex": "^.{6,}$"}},
                },
                {
                    "id": "sql-injection-risk",
                    "message": "Potential SQL injection vulnerability",
                    "severity": "CRITICAL",
                    "languages": ["python", "javascript", "java"],
                    "pattern": "execute($QUERY)",
                    "constraints": {"QUERY": {"regex": ".*\\+.*"}},
                },
                {
                    "id": "eval-usage",
                    "message": "Use of eval() function detected - potential security risk",
                    "severity": "HIGH",
                    "languages": ["python", "javascript"],
                    "pattern": "eval($ARG)",
                },
                {
                    "id": "unsafe-deserialization",
                    "message": "Unsafe deserialization detected",
                    "severity": "HIGH",
                    "languages": ["python"],
                    "pattern": "pickle.loads($DATA)",
                },
                {
                    "id": "command-injection",
                    "message": "Potential command injection vulnerability",
                    "severity": "CRITICAL",
                    "languages": ["python"],
                    "pattern": "os.system($CMD)",
                    "constraints": {"CMD": {"regex": ".*\\+.*"}},
                },
            ]
        }

    async def analyze(
        self, diff_content: str, files_info: Dict[str, Any], temp_dir: Path
    ) -> AnalysisResult:
        """
        Run AST-Grep analysis on the provided diff.

        Args:
            diff_content: The diff content to analyze
            files_info: Information about changed files
            temp_dir: Temporary directory for analysis

        Returns:
            Analysis result with security findings
        """
        started_at = datetime.now(timezone.utc)

        try:
            self.logger.info("Starting AST-Grep analysis")

            # Extract and write changed files to temp directory
            changed_files = await self._extract_changed_files(
                diff_content, files_info, temp_dir
            )

            if not changed_files:
                self.logger.info("No supported files found for AST-Grep analysis")
                return AnalysisResult(
                    analyzer_type=self.analyzer_type,
                    analyzer_version=self.version,
                    started_at=started_at,
                    completed_at=datetime.now(timezone.utc),
                    duration_seconds=0,
                    success=True,
                    total_findings=0,
                    files_analyzed=[],
                    error_message=None,
                    raw_output="",
                )

            # Create rules configuration file
            rules_file = await self._create_rules_file(temp_dir)

            # Run AST-Grep
            ast_grep_output = await self._run_ast_grep(
                temp_dir, rules_file, changed_files
            )

            # Parse results
            findings = self._parse_ast_grep_output(ast_grep_output, temp_dir)

            completed_at = datetime.now(timezone.utc)
            duration = (completed_at - started_at).total_seconds()

            result = AnalysisResult(
                analyzer_type=self.analyzer_type,
                analyzer_version=self.version,
                started_at=started_at,
                completed_at=completed_at,
                duration_seconds=duration,
                findings=findings,
                files_analyzed=changed_files,
                success=True,
                total_findings=len(findings),
                raw_output=ast_grep_output,
                error_message=None,
            )

            self.logger.info(
                f"AST-Grep analysis completed: {len(findings)} findings in {duration:.2f}s"
            )
            return result

        except Exception as e:
            completed_at = datetime.now(timezone.utc)
            duration = (completed_at - started_at).total_seconds()

            self.logger.error(f"AST-Grep analysis failed: {e}")

            return AnalysisResult(
                analyzer_type=self.analyzer_type,
                analyzer_version=self.version,
                started_at=started_at,
                completed_at=completed_at,
                duration_seconds=duration,
                success=False,
                error_message=str(e),
                total_findings=0,
                files_analyzed=[],
                raw_output="",
            )

    async def _extract_changed_files(
        self, _diff_content: str, files_info: Dict[str, Any], temp_dir: Path
    ) -> List[str]:
        """
        Extract changed files from diff and write them to temp directory.

        Args:
            diff_content: The diff content
            files_info: File information from GitHub API
            temp_dir: Temporary directory to write files

        Returns:
            List of file paths that were extracted
        """
        changed_files = []

        # Get list of changed files from files_info
        files = files_info.get("files", [])

        for file_info in files:
            filename = file_info.get("filename", "")
            status = file_info.get("status", "")

            # Skip deleted files
            if status == "removed":
                continue

            # Check if file is supported
            if not self.should_analyze_file(filename):
                continue

            # For now, we'll create placeholder files since we only have the diff
            # In a real implementation, you'd fetch the full file content
            file_path = temp_dir / filename
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Create a placeholder file with some content
            # This is a simplified approach - in production you'd want to reconstruct
            # the actual file content from the diff or fetch it from the repository
            file_path.write_text(
                f"# Placeholder for {filename}\n# Analysis based on diff content\n"
            )

            changed_files.append(str(file_path.relative_to(temp_dir)))

        return changed_files

    async def _create_rules_file(self, temp_dir: Path) -> Path:
        """
        Create AST-Grep rules configuration file.

        Args:
            temp_dir: Temporary directory

        Returns:
            Path to the created rules file
        """
        rules_file = temp_dir / "ast-grep-rules.yml"

        # Convert rules to YAML format for AST-Grep
        import yaml

        try:
            with open(rules_file, "w") as f:
                yaml.dump(self.rules_config, f, default_flow_style=False)
        except ImportError:
            # Fallback to JSON if PyYAML is not available
            rules_file = temp_dir / "ast-grep-rules.json"
            with open(rules_file, "w") as f:
                json.dump(self.rules_config, f, indent=2)

        return rules_file

    async def _run_ast_grep(
        self, temp_dir: Path, rules_file: Path, changed_files: List[str]
    ) -> str:
        """
        Run AST-Grep on the specified files.

        Args:
            temp_dir: Directory containing files to analyze
            rules_file: Path to rules configuration file
            changed_files: List of files to analyze

        Returns:
            AST-Grep output as JSON string
        """
        cmd = ["ast-grep", "scan", "--json", "--config", str(rules_file)]

        # Add specific files or scan the entire directory
        if changed_files:
            cmd.extend(changed_files)
        else:
            cmd.append(".")

        self.logger.debug(f"Running AST-Grep command: {' '.join(cmd)}")

        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=temp_dir,
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=self.timeout
            )

            if process.returncode != 0:
                error_msg = stderr.decode("utf-8") if stderr else "Unknown error"
                # AST-Grep might return non-zero for findings, so check stderr
                if "error" in error_msg.lower() or "failed" in error_msg.lower():
                    raise AnalyzerError(f"AST-Grep failed: {error_msg}")

            return stdout.decode("utf-8")

        except asyncio.TimeoutError:
            raise AnalyzerError(
                f"AST-Grep analysis timed out after {self.timeout} seconds"
            )
        except Exception as e:
            raise AnalyzerError(f"Failed to run AST-Grep: {e}")

    def _parse_ast_grep_output(
        self, output: str, temp_dir: Path
    ) -> List[SecurityFinding]:
        """
        Parse AST-Grep JSON output into SecurityFinding objects.

        Args:
            output: AST-Grep JSON output
            temp_dir: Temporary directory used for analysis

        Returns:
            List of security findings
        """
        findings = []

        if not output.strip():
            return findings

        try:
            # AST-Grep output might be line-delimited JSON
            for line in output.strip().split("\n"):
                if line.strip():
                    result = json.loads(line)
                    finding = self._create_finding_from_result(result, temp_dir)
                    if finding:
                        findings.append(finding)

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse AST-Grep output as JSON: {e}")
            # Try parsing as a single JSON object
            try:
                data = json.loads(output)
                if isinstance(data, list):
                    for result in data:
                        finding = self._create_finding_from_result(result, temp_dir)
                        if finding:
                            findings.append(finding)
                else:
                    finding = self._create_finding_from_result(data, temp_dir)
                    if finding:
                        findings.append(finding)
            except json.JSONDecodeError:
                raise AnalyzerError(f"Invalid AST-Grep output format: {e}")
        except Exception as e:
            self.logger.error(f"Error parsing AST-Grep results: {e}")
            raise AnalyzerError(f"Failed to parse AST-Grep results: {e}")

        return findings

    def _create_finding_from_result(
        self, result: Dict[str, Any], temp_dir: Path
    ) -> Optional[SecurityFinding]:
        """
        Create a SecurityFinding from an AST-Grep result.

        Args:
            result: Single AST-Grep result
            temp_dir: Temporary directory used for analysis

        Returns:
            SecurityFinding object or None if parsing fails
        """
        try:
            # Extract basic information
            rule_id = result.get("rule_id", result.get("id", "unknown"))
            message = result.get("message", "")

            # Extract location information
            file_path = result.get("file", result.get("path", ""))
            range_info = result.get("range", {})
            start_line = range_info.get("start", {}).get("line", 1)
            end_line = range_info.get("end", {}).get("line", start_line)
            start_col = range_info.get("start", {}).get("column")
            end_col = range_info.get("end", {}).get("column")

            # Make path relative to temp_dir
            try:
                relative_path = Path(file_path).relative_to(temp_dir)
                file_path = str(relative_path)
            except ValueError:
                pass

            # Extract severity
            severity = self._map_ast_grep_severity(result.get("severity", "INFO"))

            # Extract code snippet
            code_snippet = result.get("text", "")

            # Create location
            location = CodeLocation(
                file_path=file_path,
                line_start=start_line,
                line_end=end_line if end_line != start_line else None,
                column_start=start_col,
                column_end=end_col,
            )

            # Create finding
            finding = SecurityFinding(
                analyzer_type=self.analyzer_type,
                rule_id=rule_id,
                rule_name=rule_id.replace("-", " ").title(),
                severity=severity,
                location=location,
                title=f"AST-Grep: {rule_id}",
                description=message,
                message=message,
                code_snippet=code_snippet,
                metadata=result,
                fix_suggestion=None,
                confidence=None,
            )

            return finding

        except Exception as e:
            self.logger.warning(f"Failed to parse AST-Grep result: {e}")
            return None

    def _map_ast_grep_severity(self, ast_grep_severity: str) -> SeverityLevel:
        """
        Map AST-Grep severity to our SeverityLevel enum.

        Args:
            ast_grep_severity: AST-Grep severity string

        Returns:
            Mapped severity level
        """
        severity_map = {
            "CRITICAL": SeverityLevel.CRITICAL,
            "HIGH": SeverityLevel.HIGH,
            "MEDIUM": SeverityLevel.MEDIUM,
            "LOW": SeverityLevel.LOW,
            "INFO": SeverityLevel.INFO,
        }

        return severity_map.get(ast_grep_severity.upper(), SeverityLevel.INFO)
