from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class GitHubPRWebhookData(BaseModel):
    """
    Pydantic model for GitHub Pull Request webhook data.

    This model represents the data structure received when a GitHub PR is opened,
    updated, or closed via webhook.
    """

    # Basic PR information
    id: int = Field(..., description="Pull request ID")
    number: int = Field(..., description="Pull request number")
    title: str = Field(..., description="Pull request title")
    state: str = Field(..., description="Pull request state (open, closed, etc.)")
    action: str = Field(
        ..., description="Webhook action (opened, synchronize, closed, etc.)"
    )

    # Repository and user information
    repository: str = Field(..., description="Full repository name (owner/repo)")
    author: str = Field(..., description="PR author username")
    sender: str = Field(..., description="Webhook sender username")

    # URLs and references
    url: str = Field(..., description="HTML URL to the pull request")
    base_branch: str = Field(..., description="Base branch name")
    head_branch: str = Field(..., description="Head branch name")

    # PR metadata
    draft: bool = Field(..., description="Whether the PR is a draft")
    mergeable: Optional[bool] = Field(None, description="Whether the PR is mergeable")

    # Timestamps
    created_at: datetime = Field(..., description="PR creation timestamp")
    updated_at: datetime = Field(..., description="PR last update timestamp")

    # Diff and patch information
    diff_url: str = Field(..., description="URL to get the diff")
    patch_url: str = Field(..., description="URL to get the patch")

    # Change statistics
    commits: int = Field(..., description="Number of commits in the PR")
    additions: int = Field(..., description="Number of lines added")
    deletions: int = Field(..., description="Number of lines deleted")
    changed_files: int = Field(..., description="Number of files changed")

    # Additional URLs for detailed information
    commits_url: str = Field(..., description="URL to get commits")
    review_comments_url: str = Field(..., description="URL to get review comments")
    statuses_url: str = Field(..., description="URL to get status checks")

    class Config:
        """Pydantic configuration"""

        json_encoders = {datetime: lambda v: v.isoformat()}

    def __str__(self) -> str:
        return f"PR #{self.number}: {self.title} ({self.repository})"

    def __repr__(self) -> str:
        return f"GitHubPRWebhookData(id={self.id}, number={self.number}, repository='{self.repository}')"
