"""MongoDB service for managing organization and installation data."""

import logging
from typing import Dict, List, Optional

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import PyMongoError

from platyfend_ai.config.settings import settings

logger = logging.getLogger(__name__)


class MongoDBService:
    """Base MongoDB service for database operations."""

    def __init__(
        self, mongodb_uri: Optional[str] = None, database_name: Optional[str] = None
    ):
        """
        Initialize MongoDB service.

        Args:
            mongodb_uri: MongoDB connection URI
            database_name: Database name to use
        """
        self.mongodb_uri = mongodb_uri or settings.mongodb_uri
        self.database_name = database_name or settings.mongodb_database
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None

        if not self.mongodb_uri:
            logger.warning("No MongoDB URI configured - MongoDB operations will fail")

    async def connect(self) -> None:
        """Connect to MongoDB."""
        if not self.mongodb_uri:
            raise ValueError("MongoDB URI is required for connection")

        try:
            self.client = AsyncIOMotorClient(
                self.mongodb_uri,
                serverSelectionTimeoutMS=settings.mongodb_timeout * 1000,
            )
            self.database = self.client[self.database_name]

            # Test the connection
            await self.client.admin.command("ping")
            logger.info(f"Connected to MongoDB database: {self.database_name}")

        except PyMongoError as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise

    async def disconnect(self) -> None:
        """Disconnect from MongoDB."""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")

    async def get_collection(self, collection_name: str):
        """Get a collection from the database."""
        if self.database is None:
            await self.connect()

        if self.database is None:
            raise RuntimeError("Failed to establish database connection")

        return self.database[collection_name]


class OrganizationService:
    """Service for managing GitHub organization and installation data."""

    def __init__(self, mongodb_service: Optional[MongoDBService] = None):
        """
        Initialize organization service.

        Args:
            mongodb_service: MongoDB service instance
        """
        self.mongodb_service = mongodb_service or MongoDBService()
        self.collection_name = "organizations"

    async def get_installation_id_by_repo(self, repository_name: str) -> Optional[int]:
        """
        Get GitHub App installation ID for a repository.

        Args:
            repository_name: Repository name in format "owner/repo"

        Returns:
            Installation ID if found, None otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            # Query for organization by owner name
            query = {"repos.full_name": repository_name}
            organization = await collection.find_one(query)

            if organization and "installation_id" in organization:
                installation_id = organization["installation_id"]
                logger.info(
                    f"Found installation ID {installation_id} for repository {repository_name}"
                )
                return int(installation_id)
            else:
                logger.warning(
                    f"No installation ID found for repository {repository_name}"
                )
                return None

        except PyMongoError as e:
            logger.error(
                f"MongoDB error while querying installation ID for {repository_name}: {e}"
            )
            return None
        except Exception as e:
            logger.error(
                f"Unexpected error while querying installation ID for {repository_name}: {e}"
            )
            return None

    async def get_organization_by_installation_id(
        self, installation_id: int
    ) -> Optional[Dict]:
        """
        Get organization data by installation ID.

        Args:
            installation_id: GitHub App installation ID

        Returns:
            Organization document if found, None otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            query = {"installation_id": installation_id}
            organization = await collection.find_one(query)

            if organization:
                logger.info(f"Found organization for installation ID {installation_id}")
                return organization
            else:
                logger.warning(
                    f"No organization found for installation ID {installation_id}"
                )
                return None

        except PyMongoError as e:
            logger.error(
                f"MongoDB error while querying organization for installation ID {installation_id}: {e}"
            )
            return None
        except Exception as e:
            logger.error(
                f"Unexpected error while querying organization for installation ID {installation_id}: {e}"
            )
            return None

    async def list_organizations(self) -> List[Dict]:
        """
        List all organizations.

        Returns:
            List of organization documents
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            organizations = []
            async for org in collection.find({}):
                organizations.append(org)

            logger.info(f"Retrieved {len(organizations)} organizations")
            return organizations

        except PyMongoError as e:
            logger.error(f"MongoDB error while listing organizations: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error while listing organizations: {e}")
            return []

    async def upsert_organization(
        self, owner: str, installation_id: int, **kwargs
    ) -> bool:
        """
        Insert or update organization data.

        Args:
            owner: GitHub organization/user owner name
            installation_id: GitHub App installation ID
            **kwargs: Additional organization data

        Returns:
            True if successful, False otherwise
        """
        try:
            collection = await self.mongodb_service.get_collection(self.collection_name)

            organization_data = {
                "owner": owner,
                "installation_id": installation_id,
                **kwargs,
            }

            # Upsert based on owner
            result = await collection.update_one(
                {"owner": owner}, {"$set": organization_data}, upsert=True
            )

            if result.upserted_id or result.modified_count > 0:
                logger.info(
                    f"Successfully upserted organization {owner} with installation ID {installation_id}"
                )
                return True
            else:
                logger.warning(f"No changes made for organization {owner}")
                return False

        except PyMongoError as e:
            logger.error(f"MongoDB error while upserting organization {owner}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error while upserting organization {owner}: {e}")
            return False
