import logging
from typing import Any, Dict, List, Optional

from openai import Async<PERSON>penA<PERSON>

from platyfend_ai.config.settings import settings
from platyfend_ai.models.analysis import ReviewComment, SecurityFinding, SeverityLevel

logger = logging.getLogger(__name__)


class CommentGenerationError(Exception):
    """Exception raised when comment generation fails"""

    pass


class SecurityCommentGenerator:
    """Service for generating human-readable security review comments using AI"""

    def __init__(
        self, openai_api_key: Optional[str] = None, model: str = "gpt-4o-mini"
    ):
        """
        Initialize the comment generator.

        Args:
            openai_api_key: OpenAI API key for LLM integration
            model: OpenAI model to use for comment generation
        """
        self.api_key = openai_api_key or settings.openai_api_key
        self.model = model

        if not self.api_key:
            logger.warning("No OpenAI API key provided - comment generation will fail")
            self.client = None
        else:
            self.client = AsyncOpenAI(api_key=self.api_key)
            logger.info(f"Comment generator initialized with model: {model}")

    async def generate_review_comments(
        self,
        findings: List[SecurityFinding],
        pr_context: Optional[Dict[str, Any]] = None,
    ) -> List[ReviewComment]:
        """
        Generate review comments for security findings.

        Args:
            findings: List of security findings to generate comments for
            pr_context: Optional context about the PR (title, description, etc.)

        Returns:
            List of generated review comments
        """
        if not findings:
            return []

        logger.info(f"Generating comments for {len(findings)} findings")

        comments = []

        # Group findings by file and severity for better organization
        grouped_findings = self._group_findings(findings)

        for file_path, file_findings in grouped_findings.items():
            # Generate comments for this file
            file_comments = await self._generate_file_comments(
                file_findings, file_path, pr_context
            )
            comments.extend(file_comments)

        # Generate summary comment if there are multiple high-severity findings
        summary_comment = await self._generate_summary_comment(findings, pr_context)
        if summary_comment:
            comments.append(summary_comment)

        logger.info(f"Generated {len(comments)} review comments")
        return comments

    async def generate_no_findings_message(
        self, pr_context: Optional[Dict[str, Any]] = None
    ) -> List[ReviewComment]:
        """
        Generate a positive security message when no findings are detected.

        Args:
            pr_context: Optional context about the PR (title, description, etc.)

        Returns:
            List containing a single positive review comment
        """
        logger.info("Generating positive security message for clean PR")

        # Use AI to generate a catchy, personalized message
        comment_body = await self._generate_ai_no_findings_comment(pr_context)

        # Create a general PR comment (not tied to specific file/line)
        comment = ReviewComment(
            body=comment_body,
            file_path=None,
            line=None,
            finding_ids=[],
            severity=None,
            comment_type="security_clean",
        )

        return [comment]

    def _group_findings(
        self, findings: List[SecurityFinding]
    ) -> Dict[str, List[SecurityFinding]]:
        """Group findings by file path"""
        grouped = {}
        for finding in findings:
            file_path = finding.location.file_path
            if file_path not in grouped:
                grouped[file_path] = []
            grouped[file_path].append(finding)
        return grouped

    async def _generate_file_comments(
        self,
        findings: List[SecurityFinding],
        _file_path: str,
        pr_context: Optional[Dict[str, Any]] = None,
    ) -> List[ReviewComment]:
        """Generate comments for findings in a specific file"""
        comments = []

        # Sort findings by line number for better organization
        sorted_findings = sorted(findings, key=lambda f: f.location.line_start)

        # Group nearby findings to avoid comment spam
        grouped_findings = self._group_nearby_findings(sorted_findings)

        for finding_group in grouped_findings:
            if len(finding_group) == 1:
                # Single finding comment
                comment = await self._generate_single_finding_comment(
                    finding_group[0], pr_context
                )
            else:
                # Multiple findings comment
                comment = await self._generate_multiple_findings_comment(
                    finding_group, pr_context
                )

            if comment:
                comments.append(comment)

        return comments

    def _group_nearby_findings(
        self, findings: List[SecurityFinding], line_threshold: int = 5
    ) -> List[List[SecurityFinding]]:
        """Group findings that are close to each other in the same file"""
        if not findings:
            return []

        groups = []
        current_group = [findings[0]]

        for finding in findings[1:]:
            # Check if this finding is close to the last finding in current group
            last_finding = current_group[-1]
            line_diff = finding.location.line_start - last_finding.location.line_start

            if line_diff <= line_threshold:
                current_group.append(finding)
            else:
                groups.append(current_group)
                current_group = [finding]

        groups.append(current_group)
        return groups

    async def _generate_single_finding_comment(
        self, finding: SecurityFinding, pr_context: Optional[Dict[str, Any]] = None
    ) -> ReviewComment:
        """Generate a comment for a single security finding"""

        if not self.client:
            raise CommentGenerationError(
                "OpenAI API key required for comment generation"
            )

        # Use AI to generate a detailed comment
        comment_body = await self._generate_ai_comment(finding, pr_context)

        return ReviewComment(
            body=comment_body,
            file_path=finding.location.file_path,
            line=finding.location.line_start,
            finding_ids=[finding.id],
            severity=finding.severity,
            comment_type="security_review",
        )

    async def _generate_multiple_findings_comment(
        self,
        findings: List[SecurityFinding],
        pr_context: Optional[Dict[str, Any]] = None,
    ) -> ReviewComment:
        """Generate a comment for multiple related security findings"""

        if not self.client:
            raise CommentGenerationError(
                "OpenAI API key required for comment generation"
            )

        # Use AI to generate a comprehensive comment
        comment_body = await self._generate_ai_multiple_comment(findings, pr_context)

        # Use the line of the first (highest severity) finding
        primary_finding = max(findings, key=lambda f: self._severity_weight(f.severity))

        return ReviewComment(
            body=comment_body,
            file_path=primary_finding.location.file_path,
            line=primary_finding.location.line_start,
            finding_ids=[f.id for f in findings],
            severity=primary_finding.severity,
            comment_type="security_review",
        )

    async def _generate_summary_comment(
        self,
        findings: List[SecurityFinding],
        pr_context: Optional[Dict[str, Any]] = None,
    ) -> Optional[ReviewComment]:
        """Generate a summary comment for the entire PR if warranted"""

        # Only generate summary for significant findings
        critical_findings = [
            f for f in findings if f.severity == SeverityLevel.CRITICAL
        ]
        high_findings = [f for f in findings if f.severity == SeverityLevel.HIGH]

        if len(critical_findings) + len(high_findings) < 3:
            return None  # Not enough significant findings for a summary

        if not self.client:
            raise CommentGenerationError(
                "OpenAI API key required for comment generation"
            )

        comment_body = await self._generate_ai_summary_comment(findings, pr_context)

        return ReviewComment(
            body=comment_body,
            finding_ids=[f.id for f in findings],
            comment_type="security_summary",
            file_path=None,
            line=None,
            severity=None,
        )

    async def _generate_ai_comment(
        self, finding: SecurityFinding, _pr_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate an AI-powered comment for a single finding"""

        system_prompt = """You are a senior security engineer reviewing code. Generate a helpful, professional comment about the security finding. Be specific about the risk and provide actionable remediation advice. Use a friendly but authoritative tone."""

        user_prompt = f"""
Security Finding Details:
- Rule: {finding.rule_name} ({finding.rule_id})
- Severity: {finding.severity.value.upper()}
- Location: {finding.location.file_path}:{finding.location.line_start}
- Description: {finding.description}
- Message: {finding.message}

Code Context:
{finding.code_snippet or "No code snippet available"}

Please generate a concise but informative security review comment. Include:
1. Brief explanation of the security issue
2. Potential impact/risk
3. Specific remediation steps
4. Any relevant security best practices

Keep the tone professional but approachable. Use markdown formatting for better readability.
"""

        if not self.client:
            raise CommentGenerationError(
                "OpenAI API key required for comment generation"
            )

        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=500,
                temperature=0.3,
            )

            content = response.choices[0].message.content
            if content is None:
                raise CommentGenerationError("AI generated empty response")
            return content.strip()

        except Exception as e:
            logger.error(f"Failed to generate AI comment: {e}")
            raise CommentGenerationError(f"Failed to generate comment: {e}")

    async def _generate_ai_multiple_comment(
        self,
        findings: List[SecurityFinding],
        _pr_context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Generate an AI-powered comment for multiple findings"""

        system_prompt = """You are a senior security engineer reviewing code. Generate a comprehensive comment about multiple related security findings in the same area of code. Organize the issues clearly and provide consolidated remediation advice."""

        findings_text = "\n".join(
            [
                f"- {f.rule_name} ({f.severity.value.upper()}): {f.description}"
                for f in findings
            ]
        )

        user_prompt = f"""
Multiple Security Findings in {findings[0].location.file_path} around line {findings[0].location.line_start}:

{findings_text}

Please generate a comprehensive security review comment that:
1. Summarizes the security issues found
2. Explains the combined risk/impact
3. Provides consolidated remediation steps
4. Suggests security best practices for this code area

Use markdown formatting and organize the content clearly.
"""

        if not self.client:
            raise CommentGenerationError(
                "OpenAI API key required for comment generation"
            )

        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=800,
                temperature=0.3,
            )

            content = response.choices[0].message.content
            if content is None:
                raise CommentGenerationError("AI generated empty response")
            return content.strip()

        except Exception as e:
            logger.error(f"Failed to generate AI multiple comment: {e}")
            raise CommentGenerationError(f"Failed to generate multiple comment: {e}")

    async def _generate_ai_summary_comment(
        self,
        findings: List[SecurityFinding],
        _pr_context: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Generate an AI-powered summary comment for the entire PR"""

        system_prompt = """You are a senior security engineer providing a security review summary for a pull request. Create a professional summary that helps developers understand the overall security posture of their changes."""

        # Summarize findings by severity
        severity_counts = {}
        for finding in findings:
            severity = finding.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        findings_summary = "\n".join(
            [
                f"- {count} {severity.upper()} severity issues"
                for severity, count in sorted(
                    severity_counts.items(),
                    key=lambda x: self._severity_weight(SeverityLevel(x[0])),
                    reverse=True,
                )
            ]
        )

        user_prompt = f"""
Security Review Summary for Pull Request:

Findings Overview:
{findings_summary}

Total Issues Found: {len(findings)}

Please generate a professional security review summary that:
1. Provides an overview of the security findings
2. Highlights the most critical issues that need immediate attention
3. Gives general security recommendations
4. Encourages good security practices

Keep it concise but informative. Use markdown formatting.
"""

        if not self.client:
            raise CommentGenerationError(
                "OpenAI API key required for comment generation"
            )

        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=600,
                temperature=0.3,
            )

            content = response.choices[0].message.content
            if content is None:
                raise CommentGenerationError("AI generated empty response")
            return content.strip()

        except Exception as e:
            logger.error(f"Failed to generate AI summary comment: {e}")
            raise CommentGenerationError(f"Failed to generate summary comment: {e}")

    async def _generate_ai_no_findings_comment(
        self, pr_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate an AI-powered positive message when no security issues are found"""

        if not self.client:
            raise CommentGenerationError(
                "OpenAI API key required for comment generation"
            )

        system_prompt = """You are a friendly cybersecurity AI assistant celebrating clean, secure code. Generate an enthusiastic, catchy message with cyber security themes and emojis when no security vulnerabilities are found in a pull request. Make it fun, engaging, and encouraging while maintaining professionalism."""

        pr_info = ""
        if pr_context:
            pr_info = f"""
PR Context:
- Title: {pr_context.get('title', 'N/A')}
- Author: {pr_context.get('author', 'N/A')}
- Repository: {pr_context.get('repository', 'N/A')}
"""

        user_prompt = f"""
{pr_info}

Generate a catchy, positive security message for this pull request that passed all security scans with zero findings. Include:

1. Celebratory cyber security themed message with relevant emojis
2. Acknowledgment of good security practices
3. Encouragement to keep up the secure coding
4. Fun cyber security references or puns (optional)
5. Brief mention that this was an automated security review

Make it engaging and memorable while staying professional. Use markdown formatting and plenty of relevant emojis like 🛡️, 🔒, 🚀, ✨, 🎯, 💪, etc.
"""

        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                max_tokens=400,
                temperature=0.7,  # Higher temperature for more creative/fun responses
            )

            content = response.choices[0].message.content
            if content is None:
                raise CommentGenerationError("AI generated empty response")
            return content.strip()

        except Exception as e:
            logger.error(f"Failed to generate AI no-findings comment: {e}")
            raise CommentGenerationError(f"Failed to generate no-findings comment: {e}")

    def _severity_weight(self, severity: SeverityLevel) -> int:
        """Get numeric weight for severity level (higher = more severe)"""
        weights = {
            SeverityLevel.CRITICAL: 5,
            SeverityLevel.HIGH: 4,
            SeverityLevel.MEDIUM: 3,
            SeverityLevel.LOW: 2,
            SeverityLevel.INFO: 1,
        }
        return weights.get(severity, 0)
